"""
Location Extraction Module for Document Management System

This module provides Named Entity Recognition (NER) capabilities to extract
geographical locations from text content including PDFs and chat messages.

Features:
- Extract place names, addresses, coordinates, landmarks, and regions
- Multiple extraction methods: NER, regex patterns, coordinate detection
- Geocoding integration with caching
- Confidence scoring for extracted locations
- Integration with existing PDF processing and chat systems
"""

import os
import re
import logging
import sqlite3
import requests
import json
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime, timedelta
import time

# Try to import spaCy for NER, fall back to regex-based extraction
try:
    import spacy
    HAS_SPACY = True
    # Try to load the English model
    try:
        nlp = spacy.load("en_core_web_sm")
    except OSError:
        # Model not installed, will use regex-based extraction
        nlp = None
        HAS_SPACY = False
        logging.warning("spaCy English model not found. Using regex-based location extraction.")
except ImportError:
    HAS_SPACY = False
    nlp = None
    logging.warning("spaCy not installed. Using regex-based location extraction.")

from db_schema import DB_PATH

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
GEOCODING_CACHE_EXPIRY_DAYS = 30
NOMINATIM_BASE_URL = "https://nominatim.openstreetmap.org"
GEOCODING_DELAY = 1.0  # Delay between requests to respect rate limits

class LocationExtractor:
    """Main class for extracting geographical locations from text."""

    def __init__(self):
        self.confidence_threshold = 0.5
        self.max_locations_per_text = 50

    def extract_locations_from_text(self, text: str, context: str = None) -> List[Dict[str, Any]]:
        """
        Extract geographical locations from text using multiple methods.

        Args:
            text: The text to extract locations from
            context: Additional context for better extraction

        Returns:
            List of location dictionaries with extracted information
        """
        locations = []

        # Method 1: NER-based extraction (if spaCy is available)
        if HAS_SPACY and nlp:
            ner_locations = self._extract_with_ner(text, context)
            locations.extend(ner_locations)

        # Method 2: Regex-based extraction for coordinates and common patterns
        regex_locations = self._extract_with_regex(text, context)
        locations.extend(regex_locations)

        # Method 3: Extract addresses using pattern matching
        address_locations = self._extract_addresses(text, context)
        locations.extend(address_locations)

        # Remove duplicates and filter by confidence
        locations = self._deduplicate_locations(locations)
        locations = [loc for loc in locations if loc['confidence_score'] >= self.confidence_threshold]

        # Limit the number of locations
        if len(locations) > self.max_locations_per_text:
            locations = sorted(locations, key=lambda x: x['confidence_score'], reverse=True)
            locations = locations[:self.max_locations_per_text]

        return locations

    def _extract_with_ner(self, text: str, context: str = None) -> List[Dict[str, Any]]:
        """Extract locations using spaCy NER."""
        locations = []

        try:
            doc = nlp(text)

            for ent in doc.ents:
                if ent.label_ in ['GPE', 'LOC', 'FAC']:  # Geopolitical entity, Location, Facility
                    location_type = self._classify_location_type(ent.text, ent.label_)

                    # Skip non-geographical entities
                    if location_type is None:
                        continue

                    confidence = self._calculate_ner_confidence(ent, doc)

                    location = {
                        'location_text': ent.text.strip(),
                        'location_type': location_type,
                        'confidence_score': confidence,
                        'context_snippet': self._extract_context_snippet(text, ent.start_char, ent.end_char),
                        'extraction_method': 'ner',
                        'latitude': None,
                        'longitude': None,
                        'geocoded_address': None
                    }

                    locations.append(location)

        except Exception as e:
            logger.error(f"Error in NER extraction: {str(e)}")

        return locations

    def _extract_with_regex(self, text: str, context: str = None) -> List[Dict[str, Any]]:
        """Extract locations using regex patterns."""
        locations = []

        # Pattern for coordinates (latitude, longitude)
        coord_patterns = [
            r'(-?\d{1,3}\.\d+),\s*(-?\d{1,3}\.\d+)',  # Decimal degrees
            r'(\d{1,3}°\d{1,2}\'[\d.]*"?\s*[NS]),\s*(\d{1,3}°\d{1,2}\'[\d.]*"?\s*[EW])',  # DMS format
        ]

        for pattern in coord_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    if '°' in match.group(0):
                        # Convert DMS to decimal degrees
                        lat, lon = self._dms_to_decimal(match.group(1), match.group(2))
                    else:
                        lat, lon = float(match.group(1)), float(match.group(2))

                    # Validate coordinate ranges
                    if -90 <= lat <= 90 and -180 <= lon <= 180:
                        location = {
                            'location_text': match.group(0),
                            'location_type': 'coordinates',
                            'confidence_score': 0.9,
                            'context_snippet': self._extract_context_snippet(text, match.start(), match.end()),
                            'extraction_method': 'regex',
                            'latitude': lat,
                            'longitude': lon,
                            'geocoded_address': None
                        }
                        locations.append(location)
                except (ValueError, IndexError):
                    continue

        return locations

    def _extract_addresses(self, text: str, context: str = None) -> List[Dict[str, Any]]:
        """Extract addresses using pattern matching."""
        locations = []

        # Common address patterns
        address_patterns = [
            r'\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Drive|Dr|Lane|Ln|Way|Place|Pl)\b[^.]*?(?:\d{5}|\w{2,3}\s*\d{1,2}[A-Z]{2})',
            r'\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Drive|Dr|Lane|Ln|Way|Place|Pl)\b',
        ]

        for pattern in address_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                address_text = match.group(0).strip()
                if len(address_text) > 10:  # Filter out very short matches
                    location = {
                        'location_text': address_text,
                        'location_type': 'address',
                        'confidence_score': 0.7,
                        'context_snippet': self._extract_context_snippet(text, match.start(), match.end()),
                        'extraction_method': 'regex',
                        'latitude': None,
                        'longitude': None,
                        'geocoded_address': None
                    }
                    locations.append(location)

        return locations

    def _classify_location_type(self, location_text: str, ner_label: str) -> str:
        """
        Classify the type of location based on text and NER label, focusing on Philippine administrative divisions.
        Only extracts municipalities, cities, and barangays.
        """
        text_lower = location_text.lower()

        # Check for coordinates
        if re.search(r'-?\d+\.\d+', location_text):
            return 'coordinates'

        # Check for address indicators
        address_indicators = ['street', 'avenue', 'road', 'boulevard', 'drive', 'lane', 'way', 'place']
        if any(indicator in text_lower for indicator in address_indicators):
            return 'address'

        # Filter out non-geographical entities
        non_geographical = [
            # Organizations and institutions
            'company', 'corporation', 'inc', 'ltd', 'llc', 'organization', 'institute', 'foundation',
            'association', 'society', 'group', 'team', 'department', 'ministry', 'agency',
            # Buildings and structures (unless they're geographical landmarks)
            'building', 'tower', 'mall', 'center', 'office', 'headquarters', 'facility',
            # Events and concepts
            'conference', 'meeting', 'summit', 'forum', 'workshop', 'seminar', 'project',
            # People and titles
            'mr', 'mrs', 'dr', 'prof', 'president', 'director', 'manager', 'chairman',
            # Non-target administrative levels
            'province', 'region', 'district', 'country', 'nation', 'state'
        ]

        # Check if this is likely a non-geographical entity or non-target administrative level
        if any(indicator in text_lower for indicator in non_geographical):
            return None  # Filter out non-geographical entities and non-target levels

        # Philippine administrative division indicators
        municipality_indicators = [
            'municipality', 'municipal', 'town', 'poblacion'
        ]

        city_indicators = [
            'city', 'ciudad', 'chartered city', 'component city', 'independent city'
        ]

        barangay_indicators = [
            'barangay', 'brgy', 'barrio', 'village', 'sitio', 'purok'
        ]

        # Check for specific Philippine administrative divisions
        if any(indicator in text_lower for indicator in municipality_indicators):
            return 'municipality'
        elif any(indicator in text_lower for indicator in city_indicators):
            return 'city'
        elif any(indicator in text_lower for indicator in barangay_indicators):
            return 'barangay'

        # Check if this is a known Philippine location using pattern matching
        philippine_location_type = self._classify_philippine_location(location_text)
        if philippine_location_type:
            return philippine_location_type

        # Geographical landmarks and features (keep for reference but lower priority)
        geographical_landmarks = [
            'university', 'hospital', 'airport', 'station', 'port', 'harbor', 'bridge',
            'park', 'forest', 'mountain', 'hill', 'valley', 'river', 'lake', 'sea',
            'beach', 'island', 'peninsula', 'bay', 'gulf', 'strait', 'canal'
        ]

        if any(indicator in text_lower for indicator in geographical_landmarks):
            return 'landmark'

        # Default classification based on NER label for geographical entities
        # Only accept if it could be a Philippine administrative division
        if ner_label == 'GPE':  # Geopolitical entity (countries, cities, states)
            # Check if this could be a Philippine location
            if self._is_likely_philippine_location(location_text):
                return 'place_name'  # Will be further classified during geocoding
            else:
                return None  # Filter out non-Philippine locations
        elif ner_label == 'LOC':  # Location (geographical regions)
            # Only accept if it's likely a Philippine administrative division
            if self._is_likely_philippine_location(location_text):
                return 'place_name'
            else:
                return None
        elif ner_label == 'FAC':  # Facility (only if geographical)
            # Only classify as landmark if it's a geographical facility
            if any(indicator in text_lower for indicator in geographical_landmarks):
                return 'landmark'
            else:
                return None  # Filter out non-geographical facilities

        return None  # Default to None to filter out uncertain locations

    def _classify_philippine_location(self, location_text: str) -> Optional[str]:
        """
        Classify Philippine locations based on known patterns and common names.

        Args:
            location_text: The location text to classify

        Returns:
            str: The administrative level ('municipality', 'city', 'barangay') or None
        """
        text_lower = location_text.lower().strip()

        # Known Philippine cities (major ones)
        known_cities = [
            'manila', 'quezon city', 'caloocan', 'davao', 'cebu city', 'zamboanga',
            'antipolo', 'pasig', 'taguig', 'valenzuela', 'dasmariñas', 'calamba',
            'makati', 'marikina', 'muntinlupa', 'las piñas', 'parañaque', 'bacoor',
            'iloilo city', 'general santos', 'bacolod', 'cagayan de oro', 'baguio',
            'san jose del monte', 'malabon', 'mandaluyong', 'san juan', 'pasay'
        ]

        # Check if it's a known city
        for city in known_cities:
            if city in text_lower or text_lower in city:
                return 'city'

        # Common barangay patterns
        barangay_patterns = [
            r'\b\w+\s+(?:norte|sur|este|oeste)\b',  # Directional barangays
            r'\b(?:san|santa)\s+\w+\b',  # Saint-named barangays
            r'\b\w+\s+(?:proper|centro|central)\b',  # Central barangays
            r'\b(?:upper|lower)\s+\w+\b',  # Upper/Lower barangays
        ]

        for pattern in barangay_patterns:
            if re.search(pattern, text_lower):
                return 'barangay'

        # If it ends with common municipality suffixes
        municipality_suffixes = ['municipality', 'town']
        for suffix in municipality_suffixes:
            if text_lower.endswith(suffix):
                return 'municipality'

        return None

    def _is_likely_philippine_location(self, location_text: str) -> bool:
        """
        Determine if a location text is likely a Philippine location.

        Args:
            location_text: The location text to check

        Returns:
            bool: True if likely Philippine, False otherwise
        """
        text_lower = location_text.lower().strip()

        # Philippine-specific indicators
        philippine_indicators = [
            # Common Filipino words in place names
            'san', 'santa', 'santo', 'nueva', 'nuevo', 'norte', 'sur', 'este', 'oeste',
            'upper', 'lower', 'proper', 'centro', 'central', 'poblacion',
            # Administrative terms
            'barangay', 'brgy', 'municipality', 'city', 'barrio', 'sitio', 'purok',
            # Geographic features common in Philippines
            'isla', 'island', 'river', 'creek', 'hill', 'mount', 'mt'
        ]

        # Check for Philippine indicators
        if any(indicator in text_lower for indicator in philippine_indicators):
            return True

        # Check for Filipino naming patterns
        filipino_patterns = [
            r'\b(?:san|santa|santo)\s+\w+\b',  # Saint names
            r'\b\w+\s+(?:norte|sur|este|oeste)\b',  # Directional names
            r'\b(?:nueva|nuevo)\s+\w+\b',  # New places
        ]

        for pattern in filipino_patterns:
            if re.search(pattern, text_lower):
                return True

        # Filter out obviously non-Philippine locations
        non_philippine_indicators = [
            # Common international place indicators
            'usa', 'america', 'canada', 'australia', 'europe', 'asia', 'africa',
            'united states', 'united kingdom', 'china', 'japan', 'korea', 'thailand',
            'singapore', 'malaysia', 'indonesia', 'vietnam', 'india', 'pakistan',
            # International administrative terms
            'county', 'parish', 'prefecture', 'canton', 'oblast', 'emirate'
        ]

        if any(indicator in text_lower for indicator in non_philippine_indicators):
            return False

        # Default to True for ambiguous cases (will be filtered during geocoding)
        return True

    def _calculate_ner_confidence(self, entity, doc) -> float:
        """Calculate confidence score for NER-extracted entities."""
        base_confidence = 0.8

        # Adjust based on entity length
        if len(entity.text) < 3:
            base_confidence -= 0.3
        elif len(entity.text) > 20:
            base_confidence -= 0.1

        # Adjust based on capitalization
        if entity.text.isupper() or entity.text.islower():
            base_confidence -= 0.1

        # Adjust based on context
        if any(word.lower() in ['located', 'in', 'at', 'near', 'from']
               for word in [token.text for token in doc[max(0, entity.start-3):entity.start]]):
            base_confidence += 0.1

        return max(0.0, min(1.0, base_confidence))

    def _extract_context_snippet(self, text: str, start: int, end: int, window: int = 50) -> str:
        """Extract context around the location mention."""
        context_start = max(0, start - window)
        context_end = min(len(text), end + window)
        return text[context_start:context_end].strip()

    def _dms_to_decimal(self, dms_lat: str, dms_lon: str) -> Tuple[float, float]:
        """Convert DMS (Degrees, Minutes, Seconds) to decimal degrees."""
        def parse_dms(dms_str):
            # Extract degrees, minutes, seconds, and direction
            match = re.match(r'(\d+)°(\d+)\'([\d.]*)"?\s*([NSEW])', dms_str.strip())
            if not match:
                raise ValueError(f"Invalid DMS format: {dms_str}")

            degrees = int(match.group(1))
            minutes = int(match.group(2))
            seconds = float(match.group(3)) if match.group(3) else 0
            direction = match.group(4)

            decimal = degrees + minutes/60 + seconds/3600
            if direction in ['S', 'W']:
                decimal = -decimal

            return decimal

        return parse_dms(dms_lat), parse_dms(dms_lon)

    def _deduplicate_locations(self, locations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate locations based on text similarity."""
        unique_locations = []
        seen_texts = set()

        for location in locations:
            text_normalized = location['location_text'].lower().strip()
            if text_normalized not in seen_texts:
                seen_texts.add(text_normalized)
                unique_locations.append(location)

        return unique_locations

# Geocoding functions
def geocode_location(location_text: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
    """
    Geocode a location text to get coordinates.

    Args:
        location_text: The location text to geocode
        use_cache: Whether to use cached results

    Returns:
        Dictionary with geocoding results or None if failed
    """
    if use_cache:
        cached_result = get_cached_geocoding(location_text)
        if cached_result:
            return cached_result

    try:
        # Use Nominatim (OpenStreetMap) for geocoding
        url = f"{NOMINATIM_BASE_URL}/search"
        params = {
            'q': location_text,
            'format': 'json',
            'limit': 1,
            'addressdetails': 1
        }

        headers = {
            'User-Agent': 'DocumentManagementSystem/1.0 (<EMAIL>)'
        }

        # Respect rate limits
        time.sleep(GEOCODING_DELAY)

        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()

        data = response.json()

        if data:
            result = data[0]
            address = result.get('address', {})

            # Extract Philippine administrative divisions
            municipality = None
            barangay = None
            city = address.get('city', '') or address.get('town', '') or address.get('municipality', '')

            # Check if this is in the Philippines
            country = address.get('country', '').lower()
            is_philippines = 'philippines' in country or 'pilipinas' in country

            # Extract more specific administrative levels for Philippines
            if is_philippines:
                # Try to extract municipality and barangay from the display name
                display_name = result.get('display_name', '').lower()

                # Look for barangay indicators
                barangay_patterns = [
                    r'barangay\s+([^,]+)',
                    r'brgy\.?\s+([^,]+)',
                    r'barrio\s+([^,]+)'
                ]

                for pattern in barangay_patterns:
                    match = re.search(pattern, display_name)
                    if match:
                        barangay = match.group(1).strip().title()
                        break

                # Look for municipality indicators
                municipality_patterns = [
                    r'municipality\s+of\s+([^,]+)',
                    r'([^,]+)\s+municipality',
                    r'town\s+of\s+([^,]+)'
                ]

                for pattern in municipality_patterns:
                    match = re.search(pattern, display_name)
                    if match:
                        municipality = match.group(1).strip().title()
                        break

            geocoding_result = {
                'latitude': float(result['lat']),
                'longitude': float(result['lon']),
                'formatted_address': result.get('display_name', ''),
                'confidence_score': float(result.get('importance', 0.5)),
                'country': address.get('country', ''),
                'region': address.get('state', ''),
                'city': city,
                'municipality': municipality,
                'barangay': barangay,
                'status': 'success'
            }

            # Cache the result
            if use_cache:
                cache_geocoding_result(location_text, geocoding_result)

            return geocoding_result
        else:
            # Cache failed result to avoid repeated requests
            if use_cache:
                cache_geocoding_result(location_text, {'status': 'failed'})
            return None

    except Exception as e:
        logger.error(f"Geocoding error for '{location_text}': {str(e)}")
        if use_cache:
            cache_geocoding_result(location_text, {'status': 'failed'})
        return None

def get_cached_geocoding(location_text: str) -> Optional[Dict[str, Any]]:
    """Get cached geocoding result if available and not expired."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        cursor.execute("""
            SELECT latitude, longitude, formatted_address, confidence_score,
                   country, region, city, status, expires_at
            FROM geocoding_cache
            WHERE location_query = ? AND (expires_at IS NULL OR expires_at > ?)
        """, (location_text, datetime.now()))

        result = cursor.fetchone()
        conn.close()

        if result:
            if result[7] == 'failed':
                return {'status': 'failed'}

            return {
                'latitude': result[0],
                'longitude': result[1],
                'formatted_address': result[2],
                'confidence_score': result[3],
                'country': result[4] or '',
                'region': result[5] or '',
                'city': result[6] or '',
                'status': result[7]
            }

        return None

    except sqlite3.Error as e:
        logger.error(f"Error retrieving cached geocoding: {str(e)}")
        return None

def cache_geocoding_result(location_text: str, result: Dict[str, Any]):
    """Cache geocoding result in the database."""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()

        expires_at = datetime.now() + timedelta(days=GEOCODING_CACHE_EXPIRY_DAYS)

        cursor.execute("""
            INSERT OR REPLACE INTO geocoding_cache
            (location_query, latitude, longitude, formatted_address, confidence_score,
             country, region, city, status, expires_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            location_text,
            result.get('latitude'),
            result.get('longitude'),
            result.get('formatted_address'),
            result.get('confidence_score'),
            result.get('country'),
            result.get('region'),
            result.get('city'),
            result.get('status', 'success'),
            expires_at
        ))

        conn.commit()
        conn.close()

    except sqlite3.Error as e:
        logger.error(f"Error caching geocoding result: {str(e)}")
