<table style="width:100%; border-collapse: collapse; margin-bottom: 1rem;" border="1" class="dataframe table table-sm table-bordered table-responsive">
  <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"ead>
    <tr style="text-align: right;">
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;"></th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">5</th>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">6</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">0</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">commercial and residential.</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">1</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">T</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">able 41. Values for the computation of the FAR.</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">2</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">with</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total Actual</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Average</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">3</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Unit/Category</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Existing</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">actual</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">floor area</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Area</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Computed</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Hectares</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">4</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">structures</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">value</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">measurements</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">per unit</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">floor area</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">5</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Residential</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">15623</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">212</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">19726</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">93</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1453676</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">145</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">6</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Commercial</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">2584</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">113</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">60100.89</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">532</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">1374342</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">137</td>
    </tr>
    <tr>
      <th style="background-color: #f8f9fa; font-weight: 600; border: 1px solid #dee2e6; padding: 0.3rem;">7</th>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">Total</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;"></td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">2828018</td>
      <td style="border: 1px solid #dee2e6; padding: 0.3rem;">283</td>
    </tr>
  </tbody>
</table>