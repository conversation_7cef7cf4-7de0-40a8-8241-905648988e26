#!/usr/bin/env python3
"""
Test script for the refined geographical location extraction system.

This script tests the new Philippine administrative division extraction
and cascading deletion functionality.
"""

import os
import sys
import logging
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_philippine_location_classification():
    """Test the Philippine location classification functionality."""
    print("=== Testing Philippine Location Classification ===\n")
    
    try:
        from location_extractor import LocationExtractor
        
        extractor = LocationExtractor()
        
        # Test cases for Philippine locations
        test_cases = [
            # Municipalities
            ("Municipality of Los Baños", "municipality"),
            ("Calamba Municipality", "municipality"),
            ("Town of Alaminos", "municipality"),
            
            # Cities
            ("Quezon City", "city"),
            ("Manila City", "city"),
            ("Davao City", "city"),
            ("Cebu City", "city"),
            
            # Barangays
            ("Barangay San Antonio", "barangay"),
            ("Brgy. Poblacion", "barangay"),
            ("Barangay Santo Tomas Norte", "barangay"),
            ("Upper Balamban", "barangay"),
            
            # Should be filtered out
            ("Metro Manila", None),  # Region
            ("Laguna Province", None),  # Province
            ("United States", None),  # International
            ("Microsoft Corporation", None),  # Organization
        ]
        
        for location_text, expected_type in test_cases:
            # Test classification
            classified_type = extractor._classify_philippine_location(location_text)
            
            # Test if it's likely Philippine
            is_philippine = extractor._is_likely_philippine_location(location_text)
            
            print(f"Location: {location_text}")
            print(f"  Expected: {expected_type}")
            print(f"  Classified: {classified_type}")
            print(f"  Is Philippine: {is_philippine}")
            print(f"  Result: {'✓' if classified_type == expected_type else '✗'}")
            print()
        
        print("Philippine location classification test completed.\n")
        
    except Exception as e:
        print(f"Error in Philippine location classification test: {str(e)}\n")

def test_location_extraction():
    """Test location extraction from sample text."""
    print("=== Testing Location Extraction ===\n")
    
    try:
        from location_extractor import LocationExtractor
        
        extractor = LocationExtractor()
        
        # Sample text with Philippine locations
        sample_text = """
        The study was conducted in Barangay San Antonio, Municipality of Los Baños, 
        Laguna Province. Data collection sites included Quezon City and Davao City.
        Additional surveys were conducted in Brgy. Poblacion and the town of Alaminos.
        The research also covered areas in Metro Manila and various municipalities
        across the Philippines.
        """
        
        print("Sample text:")
        print(sample_text)
        print("\nExtracted locations:")
        
        locations = extractor.extract_locations_from_text(sample_text)
        
        for i, location in enumerate(locations, 1):
            print(f"{i}. {location['location_text']}")
            print(f"   Type: {location['location_type']}")
            print(f"   Confidence: {location['confidence_score']:.2f}")
            print(f"   Method: {location['extraction_method']}")
            print(f"   Context: {location['context_snippet'][:50]}...")
            print()
        
        print(f"Total locations extracted: {len(locations)}\n")
        
    except Exception as e:
        print(f"Error in location extraction test: {str(e)}\n")

def test_database_operations():
    """Test database operations for location data."""
    print("=== Testing Database Operations ===\n")
    
    try:
        from db_utils import save_extracted_location, delete_location_by_id, cleanup_orphaned_locations
        from db_schema import migrate_location_schema
        
        # Run migration first
        print("Running location schema migration...")
        if migrate_location_schema():
            print("✓ Migration completed successfully")
        else:
            print("✗ Migration failed")
            return
        
        # Test saving a location
        test_location = {
            'location_text': 'Test Barangay San Jose',
            'location_type': 'barangay',
            'confidence_score': 0.9,
            'context_snippet': 'This is a test location for the system',
            'extraction_method': 'ner',
            'latitude': 14.1648,
            'longitude': 121.2413,
            'geocoded_address': 'Test Barangay San Jose, Los Baños, Laguna, Philippines',
            'country': 'Philippines',
            'region': 'Calabarzon',
            'city': 'Los Baños',
            'municipality': 'Los Baños',
            'barangay': 'San Jose'
        }
        
        print("Saving test location...")
        location_id = save_extracted_location(test_location)
        
        if location_id:
            print(f"✓ Location saved with ID: {location_id}")
            
            # Test deleting the location
            print("Deleting test location...")
            if delete_location_by_id(location_id):
                print("✓ Location deleted successfully")
            else:
                print("✗ Failed to delete location")
        else:
            print("✗ Failed to save location")
        
        # Test cleanup function
        print("Testing orphaned location cleanup...")
        cleanup_result = cleanup_orphaned_locations()
        print(f"✓ Cleanup completed: {cleanup_result}")
        
        print("Database operations test completed.\n")
        
    except Exception as e:
        print(f"Error in database operations test: {str(e)}\n")

def test_cascading_deletion():
    """Test cascading deletion functionality."""
    print("=== Testing Cascading Deletion ===\n")
    
    try:
        from db_utils import delete_pdf_locations
        
        # Test with a non-existent PDF (should handle gracefully)
        print("Testing deletion of non-existent PDF...")
        result = delete_pdf_locations("non_existent_file.pdf", "test_category")
        
        if result:
            print("✓ Handled non-existent PDF gracefully")
        else:
            print("✗ Failed to handle non-existent PDF")
        
        print("Cascading deletion test completed.\n")
        
    except Exception as e:
        print(f"Error in cascading deletion test: {str(e)}\n")

def main():
    """Run all tests."""
    print("Philippine Administrative Division Location Extraction System Test")
    print("=" * 70)
    print(f"Test started at: {datetime.now()}")
    print()
    
    # Run all tests
    test_philippine_location_classification()
    test_location_extraction()
    test_database_operations()
    test_cascading_deletion()
    
    print("=" * 70)
    print("All tests completed!")
    print(f"Test finished at: {datetime.now()}")

if __name__ == "__main__":
    main()
